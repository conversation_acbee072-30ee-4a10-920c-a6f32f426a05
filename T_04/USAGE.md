# 使用指南

## 快速开始

### 1. 演示模式（推荐首次使用）

```bash
python3 demo.py
```

这个模式不需要任何API密钥，可以让您快速了解整个工作流程。

### 2. 完整功能使用

#### 步骤1：安装依赖

```bash
python3 setup.py
```

或者手动安装：

```bash
pip3 install -r requirements.txt
```

#### 步骤2：配置API密钥

复制配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入您的API密钥：

```
OPENAI_API_KEY=your_openai_api_key_here
STABILITY_API_KEY=your_stability_api_key_here  # 可选
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here  # 可选
```

#### 步骤3：测试功能

```bash
python3 test_basic.py
```

#### 步骤4：开始生成

```bash
python3 main.py
```

## API密钥获取

### OpenAI API（必需）

1. 访问 https://platform.openai.com/
2. 注册账户
3. 创建API密钥
4. 确保账户有足够余额

### Stability AI API（可选，用于高质量图像）

1. 访问 https://platform.stability.ai/
2. 注册账户
3. 获取API密钥

### ElevenLabs API（可选，用于高质量语音）

1. 访问 https://elevenlabs.io/
2. 注册账户
3. 获取API密钥

## 使用技巧

### 提示词建议

好的提示词示例：
- "枸杞有什么养生功效，帮我以此为选题，生成一篇300字左右的短视频文案"
- "蜂蜜的美容功效，请生成一个适合短视频的养生文案"
- "冬季养生茶配方，生成一个温暖的养生视频文案"

### 输出文件说明

生成的文件会保存在以下位置：

- `output/videos/` - 最终视频文件
- `output/images/` - 分镜图像
- `output/audio/` - 音频文件
- `output/` - 文案和分镜文本

### 自定义配置

您可以在 `config.py` 中修改：

- 视频分辨率（默认1080x1920）
- 图像显示时长（默认3秒）
- 语言设置（默认中文）
- 输出目录路径

## 常见问题

### Q: 为什么图像生成失败？

A: 可能的原因：
1. 没有配置图像生成API密钥
2. API余额不足
3. 网络连接问题

解决方案：程序会自动生成占位图像

### Q: 语音合成质量不好？

A: 默认使用免费的gTTS，质量有限。建议：
1. 配置ElevenLabs API获得更好的语音质量
2. 或者手动替换生成的音频文件

### Q: 视频合成失败？

A: 检查：
1. 是否安装了moviepy
2. 系统是否有足够的内存
3. 输出目录是否有写入权限

### Q: API调用失败？

A: 检查：
1. API密钥是否正确
2. 账户余额是否充足
3. 网络连接是否正常

## 性能优化

1. **使用SSD存储**：提高文件读写速度
2. **充足内存**：视频处理需要较多内存
3. **稳定网络**：确保API调用成功
4. **批量处理**：一次生成多个视频时，可以复用某些资源

## 扩展功能

### 添加自定义背景音乐

1. 将音乐文件放入 `assets/music/` 目录
2. 修改 `audio_generator.py` 中的背景音乐路径

### 自定义字体

1. 将字体文件放入 `assets/fonts/` 目录
2. 修改 `video_composer.py` 中的字体路径

### 批量生成

创建包含多个提示词的文本文件，然后修改 `main.py` 进行批量处理。

## 技术支持

如果遇到问题：

1. 查看错误信息
2. 运行 `python3 test_basic.py` 诊断
3. 检查API密钥配置
4. 查看GitHub Issues

## 许可证

本项目仅供学习和研究使用。请遵守相关API服务的使用条款。
