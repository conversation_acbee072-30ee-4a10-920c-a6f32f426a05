import os
from gtts import gTTS
from pydub import AudioSegment
from config import Config
import requests

class AudioGenerator:
    def __init__(self):
        self.elevenlabs_api_key = Config.ELEVENLABS_API_KEY
    
    def generate_tts_with_gtts(self, text, filename):
        """
        使用gTTS生成语音
        """
        try:
            tts = gTTS(text=text, lang=Config.TTS_LANGUAGE, slow=False)
            audio_path = os.path.join(Config.AUDIO_DIR, filename)
            tts.save(audio_path)
            
            return audio_path
            
        except Exception as e:
            print(f"使用gTTS生成语音时出错: {e}")
            return None
    
    def generate_tts_with_elevenlabs(self, text, filename):
        """
        使用ElevenLabs生成高质量语音（可选）
        """
        if not self.elevenlabs_api_key:
            return None
            
        try:
            url = "https://api.elevenlabs.io/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM"  # Rachel voice
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": self.elevenlabs_api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                audio_path = os.path.join(Config.AUDIO_DIR, filename)
                with open(audio_path, 'wb') as f:
                    f.write(response.content)
                return audio_path
            else:
                print(f"ElevenLabs API错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"使用ElevenLabs生成语音时出错: {e}")
            return None
    
    def generate_voiceover(self, script):
        """
        生成配音
        """
        print("正在生成配音...")
        
        # 首先尝试使用ElevenLabs
        audio_path = self.generate_tts_with_elevenlabs(script, "voiceover.mp3")
        
        # 如果失败，使用gTTS
        if not audio_path:
            audio_path = self.generate_tts_with_gtts(script, "voiceover.mp3")
        
        if audio_path:
            print(f"配音生成成功: {audio_path}")
            return audio_path
        else:
            print("配音生成失败")
            return None
    
    def create_background_music(self, duration):
        """
        创建简单的背景音乐（或者可以使用预设的音乐文件）
        """
        try:
            # 这里可以使用预设的背景音乐文件
            # 或者生成简单的音调
            
            # 创建一个简单的静音作为占位符
            silence = AudioSegment.silent(duration=duration * 1000)  # 转换为毫秒
            
            bg_music_path = os.path.join(Config.AUDIO_DIR, "background_music.mp3")
            silence.export(bg_music_path, format="mp3")
            
            return bg_music_path
            
        except Exception as e:
            print(f"创建背景音乐时出错: {e}")
            return None
    
    def mix_audio(self, voiceover_path, background_music_path, output_path):
        """
        混合配音和背景音乐
        """
        try:
            # 加载音频文件
            voiceover = AudioSegment.from_mp3(voiceover_path)
            
            # 如果有背景音乐
            if background_music_path and os.path.exists(background_music_path):
                background = AudioSegment.from_mp3(background_music_path)
                
                # 调整背景音乐长度匹配配音
                if len(background) < len(voiceover):
                    # 如果背景音乐较短，循环播放
                    loops_needed = (len(voiceover) // len(background)) + 1
                    background = background * loops_needed
                
                # 截取到配音长度
                background = background[:len(voiceover)]
                
                # 降低背景音乐音量
                background = background - 20  # 降低20dB
                
                # 混合音频
                mixed = voiceover.overlay(background)
            else:
                mixed = voiceover
            
            # 导出混合音频
            mixed.export(output_path, format="mp3")
            
            return output_path
            
        except Exception as e:
            print(f"混合音频时出错: {e}")
            return None
