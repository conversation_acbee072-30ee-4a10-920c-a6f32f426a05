# 养生视频自动生成器

这是一个基于AI的自动养生视频生成工具，可以根据用户输入的提示词自动生成包含文案、图像、配音和背景音乐的完整视频。

## 功能特点

- 🤖 **智能文案生成**: 使用OpenAI GPT模型根据用户提示词生成专业的养生文案
- 🎨 **自动图像生成**: 根据文案内容生成10个分镜提示词，并生成对应的AI图像
- 🎵 **语音合成**: 将文案转换为自然的中文语音
- 🎬 **视频合成**: 自动将图像、配音和背景音乐合成为完整的短视频
- 📱 **竖屏格式**: 输出1080x1920分辨率，适合短视频平台

## 安装说明

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd health-video-generator
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置API密钥

复制 `.env.example` 文件为 `.env`：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入您的API密钥：

```
# 必需的API密钥
OPENAI_API_KEY=your_openai_api_key_here

# 可选的API密钥（用于更高质量的生成）
STABILITY_API_KEY=your_stability_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
```

## API密钥获取

### OpenAI API (必需)
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册账户并创建API密钥
3. 确保账户有足够的余额

### Stability AI API (可选)
1. 访问 [Stability AI Platform](https://platform.stability.ai/)
2. 注册账户并获取API密钥
3. 用于生成高质量的AI图像

### ElevenLabs API (可选)
1. 访问 [ElevenLabs](https://elevenlabs.io/)
2. 注册账户并获取API密钥
3. 用于生成高质量的语音合成

## 快速开始

### 方法一：演示模式（推荐新手）

无需API密钥，快速体验功能：

```bash
python demo.py
```

这将展示完整的工作流程，生成演示文件。

### 方法二：自动安装

运行安装脚本：

```bash
python setup.py
```

这将自动：
- 检查Python环境
- 安装所有依赖包
- 创建配置文件
- 验证安装结果

### 方法三：手动安装

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置API密钥：
```bash
cp .env.example .env
# 编辑 .env 文件，填入API密钥
```

3. 运行测试：
```bash
python test_basic.py
```

4. 开始生成：
```bash
python main.py
```

## 使用方法

### 基本使用

运行主程序：

```bash
python main.py
```

按照提示输入您的需求，例如：
```
枸杞有什么养生功效，帮我以此为选题，生成一篇300字左右的短视频文案
```

### 输出文件

程序会在以下目录生成文件：
- `output/videos/` - 最终生成的视频文件
- `output/images/` - 生成的分镜图像
- `output/audio/` - 生成的音频文件
- `output/` - 文案和分镜提示词文本文件

## 项目结构

```
health-video-generator/
├── main.py              # 主程序入口
├── demo.py              # 演示模式（无需API密钥）
├── setup.py             # 自动安装脚本
├── test_basic.py        # 基本功能测试
├── config.py            # 配置文件
├── text_generator.py    # 文案和分镜生成模块
├── image_generator.py   # 图像生成模块
├── audio_generator.py   # 音频生成模块
├── video_composer.py    # 视频合成模块
├── requirements.txt     # 依赖包列表
├── .env.example        # 环境变量示例
├── README.md           # 说明文档
└── output/             # 输出目录
    ├── videos/         # 视频文件
    ├── images/         # 图像文件
    ├── audio/          # 音频文件
    └── temp/           # 临时文件
```

## 自定义配置

您可以在 `config.py` 中修改以下设置：

- 视频分辨率和帧率
- 图像显示时长
- 音频设置
- 输出目录路径

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的API密钥是否正确
   - 确认API账户有足够的余额

2. **依赖包安装失败**
   - 确保使用Python 3.8+
   - 尝试使用虚拟环境

3. **视频生成失败**
   - 检查输出目录权限
   - 确认所有依赖包正确安装

4. **图像生成失败**
   - 如果没有Stability AI密钥，程序会尝试使用DALL-E
   - 如果都失败，会生成占位图像

### 性能优化

- 使用SSD存储以提高文件I/O性能
- 确保有足够的内存处理视频文件
- 网络连接稳定以确保API调用成功

## 许可证

本项目仅供学习和研究使用。请遵守相关API服务的使用条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
