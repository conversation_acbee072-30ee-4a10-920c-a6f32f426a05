#!/bin/bash

# 养生视频生成器 - 演示脚本
# 适用于 macOS 和 Linux

echo "========================================"
echo "养生视频生成器 - 演示模式"
echo "========================================"

# 检查Python是否安装
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 错误: 未找到Python"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ 使用Python命令: $PYTHON_CMD"

# 检查demo.py是否存在
if [ ! -f "demo.py" ]; then
    echo "❌ 错误: 未找到demo.py文件"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

echo "✅ 找到演示文件"

# 运行演示
echo ""
echo "正在启动演示模式..."
echo "提示: 直接按回车使用默认示例"
echo ""

$PYTHON_CMD demo.py

echo ""
echo "========================================"
echo "演示完成！"
echo "========================================"
echo ""
echo "下一步："
echo "1. 运行 $PYTHON_CMD setup.py 安装完整环境"
echo "2. 配置 .env 文件中的API密钥"
echo "3. 运行 $PYTHON_CMD main.py 开始真实生成"
echo ""
