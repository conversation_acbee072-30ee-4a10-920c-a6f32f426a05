import os
from moviepy.editor import *
from config import Config
import numpy as np

class VideoComposer:
    def __init__(self):
        pass
    
    def create_video_from_images(self, image_paths, audio_path, output_path):
        """
        将图像和音频合成为视频
        """
        try:
            print("正在合成视频...")
            
            # 加载音频以获取总时长
            audio_clip = AudioFileClip(audio_path)
            total_duration = audio_clip.duration
            
            # 计算每张图片的显示时长
            num_images = len(image_paths)
            image_duration = total_duration / num_images
            
            print(f"总时长: {total_duration:.2f}秒, 图片数量: {num_images}, 每张图片时长: {image_duration:.2f}秒")
            
            # 创建图片剪辑列表
            clips = []
            
            for i, image_path in enumerate(image_paths):
                if os.path.exists(image_path):
                    # 创建图片剪辑
                    img_clip = ImageClip(image_path, duration=image_duration)
                    
                    # 调整图片尺寸
                    img_clip = img_clip.resize((Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT))
                    
                    # 添加淡入淡出效果
                    if i == 0:
                        # 第一张图片只有淡入
                        img_clip = img_clip.fadein(0.5)
                    elif i == len(image_paths) - 1:
                        # 最后一张图片只有淡出
                        img_clip = img_clip.fadeout(0.5)
                    else:
                        # 中间的图片有交叉淡化
                        img_clip = img_clip.crossfadein(0.3).crossfadeout(0.3)
                    
                    clips.append(img_clip)
                else:
                    print(f"警告: 图片文件不存在: {image_path}")
            
            if not clips:
                print("错误: 没有有效的图片文件")
                return None
            
            # 连接所有图片剪辑
            video_clip = concatenate_videoclips(clips, method="compose")
            
            # 设置音频
            video_clip = video_clip.set_audio(audio_clip)
            
            # 确保视频时长与音频匹配
            video_clip = video_clip.set_duration(total_duration)
            
            # 导出视频
            print(f"正在导出视频到: {output_path}")
            video_clip.write_videofile(
                output_path,
                fps=Config.VIDEO_FPS,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # 清理资源
            video_clip.close()
            audio_clip.close()
            for clip in clips:
                clip.close()
            
            print(f"视频生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"合成视频时出错: {e}")
            return None
    
    def add_text_overlay(self, video_path, text, output_path):
        """
        为视频添加文字覆盖层（可选功能）
        """
        try:
            video = VideoFileClip(video_path)
            
            # 创建文字剪辑
            txt_clip = TextClip(
                text,
                fontsize=50,
                color='white',
                font='Arial-Bold',
                stroke_color='black',
                stroke_width=2
            ).set_position(('center', 'bottom')).set_duration(video.duration)
            
            # 合成视频
            final_video = CompositeVideoClip([video, txt_clip])
            
            # 导出
            final_video.write_videofile(
                output_path,
                fps=Config.VIDEO_FPS,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # 清理资源
            final_video.close()
            video.close()
            txt_clip.close()
            
            return output_path
            
        except Exception as e:
            print(f"添加文字覆盖层时出错: {e}")
            return None
    
    def create_slideshow_with_transitions(self, image_paths, audio_path, output_path):
        """
        创建带有过渡效果的幻灯片视频
        """
        try:
            print("正在创建带过渡效果的视频...")
            
            # 加载音频
            audio_clip = AudioFileClip(audio_path)
            total_duration = audio_clip.duration
            
            # 计算时间分配
            num_images = len(image_paths)
            transition_duration = 0.5  # 过渡时间
            image_duration = (total_duration - (num_images - 1) * transition_duration) / num_images
            
            clips = []
            
            for i, image_path in enumerate(image_paths):
                if os.path.exists(image_path):
                    # 创建图片剪辑
                    img_clip = ImageClip(image_path, duration=image_duration + transition_duration)
                    img_clip = img_clip.resize((Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT))
                    
                    # 设置开始时间
                    start_time = i * (image_duration + transition_duration)
                    img_clip = img_clip.set_start(start_time)
                    
                    # 添加淡入淡出
                    if i > 0:
                        img_clip = img_clip.fadein(transition_duration)
                    if i < num_images - 1:
                        img_clip = img_clip.fadeout(transition_duration)
                    
                    clips.append(img_clip)
            
            # 合成视频
            video_clip = CompositeVideoClip(clips, size=(Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT))
            video_clip = video_clip.set_audio(audio_clip)
            video_clip = video_clip.set_duration(total_duration)
            
            # 导出视频
            video_clip.write_videofile(
                output_path,
                fps=Config.VIDEO_FPS,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # 清理资源
            video_clip.close()
            audio_clip.close()
            for clip in clips:
                clip.close()
            
            return output_path
            
        except Exception as e:
            print(f"创建过渡视频时出错: {e}")
            return None
