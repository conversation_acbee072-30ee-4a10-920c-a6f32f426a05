#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示脚本 - 不依赖外部API的基本功能演示
用于展示项目结构和基本工作流程
"""

import os
import time
from datetime import datetime
from config import Config

class DemoGenerator:
    """演示版本的视频生成器"""
    
    def __init__(self):
        Config.create_directories()
    
    def generate_demo_script(self, user_prompt):
        """生成演示文案"""
        print(f"根据提示词生成文案: {user_prompt}")
        
        # 模拟文案生成
        demo_script = """
枸杞，这个被誉为"红宝石"的养生佳品，承载着千年的中医智慧。每天一小把枸杞，就像给身体注入了活力的源泉。

枸杞富含丰富的胡萝卜素、维生素C和多种氨基酸，能够有效滋养肝肾，明目养颜。现代研究发现，枸杞中的枸杞多糖具有增强免疫力的神奇功效。

无论是泡茶、煮粥，还是直接嚼食，枸杞都能为我们的健康保驾护航。特别是对于经常用眼的朋友们，枸杞更是不可多得的护眼良品。

让我们一起拥抱这份来自大自然的馈赠，用枸杞点亮健康人生的每一天。记住，养生从一颗小小的枸杞开始！
        """.strip()
        
        return demo_script
    
    def generate_demo_scenes(self, script):
        """生成演示分镜"""
        print("生成分镜提示词...")
        
        demo_scenes = [
            "一把鲜红的枸杞散落在木质桌面上，温暖的阳光洒在上面",
            "古朴的中医药房，老中医正在称量枸杞，背景是传统的药柜",
            "显微镜下的枸杞细胞结构，展现其丰富的营养成分",
            "一位年轻女性在厨房里将枸杞加入养生茶中，画面温馨",
            "枸杞种植园的日出景象，枸杞树上挂满红色果实",
            "现代实验室中，科研人员正在研究枸杞的营养价值",
            "一家人围坐餐桌，享用加了枸杞的养生粥，其乐融融",
            "办公室白领疲劳的眼睛特写，然后切换到枸杞茶的画面",
            "传统中医理论图解，展示枸杞对肝肾的滋养作用",
            "健康活力的人们在户外运动，手中拿着枸杞养生茶"
        ]
        
        return demo_scenes
    
    def create_demo_images(self, scene_prompts):
        """创建演示图像"""
        print("生成演示图像...")
        
        image_paths = []
        
        for i, prompt in enumerate(scene_prompts):
            print(f"创建第{i+1}张图像: {prompt[:30]}...")
            
            # 创建占位图像
            filename = f"demo_scene_{i+1:02d}.png"
            image_path = self.create_placeholder_image(filename, f"分镜 {i+1}", prompt)
            image_paths.append(image_path)
            
            # 模拟生成时间
            time.sleep(0.5)
        
        return image_paths
    
    def create_placeholder_image(self, filename, title, description):
        """创建占位图像"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建图像
            img = Image.new('RGB', (Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT), 
                          color=(240, 248, 255))  # 淡蓝色背景
            draw = ImageDraw.Draw(img)
            
            # 尝试加载字体
            try:
                title_font = ImageFont.truetype("arial.ttf", 80)
                desc_font = ImageFont.truetype("arial.ttf", 40)
            except:
                title_font = ImageFont.load_default()
                desc_font = ImageFont.load_default()
            
            # 绘制标题
            title_bbox = draw.textbbox((0, 0), title, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_x = (Config.VIDEO_WIDTH - title_width) // 2
            title_y = Config.VIDEO_HEIGHT // 3
            
            draw.text((title_x, title_y), title, fill=(50, 50, 150), font=title_font)
            
            # 绘制描述（分行显示）
            words = description.split()
            lines = []
            current_line = ""
            
            for word in words:
                test_line = current_line + " " + word if current_line else word
                test_bbox = draw.textbbox((0, 0), test_line, font=desc_font)
                test_width = test_bbox[2] - test_bbox[0]
                
                if test_width <= Config.VIDEO_WIDTH - 100:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word
            
            if current_line:
                lines.append(current_line)
            
            # 绘制描述文本
            desc_y = title_y + 150
            for line in lines[:3]:  # 最多显示3行
                line_bbox = draw.textbbox((0, 0), line, font=desc_font)
                line_width = line_bbox[2] - line_bbox[0]
                line_x = (Config.VIDEO_WIDTH - line_width) // 2
                
                draw.text((line_x, desc_y), line, fill=(100, 100, 100), font=desc_font)
                desc_y += 60
            
            # 保存图像
            image_path = os.path.join(Config.IMAGES_DIR, filename)
            img.save(image_path)
            
            return image_path
            
        except Exception as e:
            print(f"创建占位图像失败: {e}")
            return None
    
    def create_demo_audio(self, script):
        """创建演示音频文件"""
        print("生成演示音频...")
        
        try:
            # 创建一个简单的文本文件作为音频占位符
            audio_path = os.path.join(Config.AUDIO_DIR, "demo_audio.txt")
            with open(audio_path, 'w', encoding='utf-8') as f:
                f.write("演示音频文件\n")
                f.write("实际使用时，这里会是根据以下文案生成的语音文件：\n\n")
                f.write(script)
            
            print(f"演示音频文件创建成功: {audio_path}")
            return audio_path
            
        except Exception as e:
            print(f"创建演示音频失败: {e}")
            return None
    
    def generate_demo_video(self, user_prompt):
        """生成演示视频（模拟流程）"""
        print("=" * 50)
        print("演示：养生视频自动生成流程")
        print("=" * 50)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 步骤1: 生成文案
            print("\n步骤1: 生成文案...")
            script = self.generate_demo_script(user_prompt)
            print(f"✅ 文案生成完成 ({len(script)}字)")
            
            # 保存文案
            script_path = os.path.join(Config.OUTPUT_DIR, f"demo_script_{timestamp}.txt")
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script)
            
            # 步骤2: 生成分镜
            print("\n步骤2: 生成分镜提示词...")
            scenes = self.generate_demo_scenes(script)
            print(f"✅ 分镜生成完成 ({len(scenes)}个)")
            
            # 保存分镜
            scenes_path = os.path.join(Config.OUTPUT_DIR, f"demo_scenes_{timestamp}.txt")
            with open(scenes_path, 'w', encoding='utf-8') as f:
                for i, scene in enumerate(scenes, 1):
                    f.write(f"{i}. {scene}\n")
            
            # 步骤3: 生成图像
            print("\n步骤3: 生成图像...")
            image_paths = self.create_demo_images(scenes)
            print(f"✅ 图像生成完成 ({len(image_paths)}张)")
            
            # 步骤4: 生成音频
            print("\n步骤4: 生成音频...")
            audio_path = self.create_demo_audio(script)
            print("✅ 音频生成完成")
            
            # 步骤5: 模拟视频合成
            print("\n步骤5: 视频合成...")
            print("📹 正在合成视频...")
            time.sleep(2)  # 模拟处理时间
            
            # 创建演示视频信息文件
            video_info_path = os.path.join(Config.VIDEOS_DIR, f"demo_video_info_{timestamp}.txt")
            with open(video_info_path, 'w', encoding='utf-8') as f:
                f.write("演示视频信息\n")
                f.write("=" * 30 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"用户提示词: {user_prompt}\n")
                f.write(f"文案文件: {script_path}\n")
                f.write(f"分镜文件: {scenes_path}\n")
                f.write(f"图像数量: {len(image_paths)}\n")
                f.write(f"音频文件: {audio_path}\n")
                f.write("\n注意: 这是演示模式，实际使用需要配置API密钥\n")
            
            print("✅ 演示视频生成完成")
            
            # 显示结果
            print("\n" + "=" * 50)
            print("演示完成！生成的文件：")
            print("=" * 50)
            print(f"📄 文案: {script_path}")
            print(f"🎬 分镜: {scenes_path}")
            print(f"🖼️  图像: {Config.IMAGES_DIR} ({len(image_paths)}张)")
            print(f"🎵 音频: {audio_path}")
            print(f"📹 视频信息: {video_info_path}")
            print("=" * 50)
            
            return video_info_path
            
        except Exception as e:
            print(f"演示过程出错: {e}")
            return None

def main():
    """主函数"""
    print("养生视频生成器 - 演示模式")
    print("(此模式不需要API密钥，用于展示基本功能)")
    print("=" * 50)
    
    # 创建演示生成器
    demo_gen = DemoGenerator()
    
    # 获取用户输入
    print("\n请输入您的提示词（或直接按回车使用默认示例）:")
    user_input = input("> ").strip()
    
    if not user_input:
        user_input = "枸杞有什么养生功效，帮我以此为选题，生成一篇300字左右的短视频文案"
        print(f"使用默认提示词: {user_input}")
    
    # 生成演示视频
    result = demo_gen.generate_demo_video(user_input)
    
    if result:
        print(f"\n🎉 演示成功完成！")
        print("\n💡 要生成真实的视频，请：")
        print("1. 运行 python setup.py 安装依赖")
        print("2. 配置 .env 文件中的API密钥")
        print("3. 运行 python main.py 开始真实生成")
    else:
        print("\n❌ 演示过程出现问题")

if __name__ == "__main__":
    main()
