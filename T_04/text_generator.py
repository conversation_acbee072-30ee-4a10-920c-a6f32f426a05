import openai
from config import Config
import re

class TextGenerator:
    def __init__(self):
        openai.api_key = Config.OPENAI_API_KEY
        self.client = openai.OpenAI(api_key=Config.OPENAI_API_KEY)
    
    def generate_script(self, user_prompt):
        """
        根据用户提示词生成养生视频文案
        """
        system_prompt = """
        你是一个专业的养生内容创作者。请根据用户的提示词，生成一篇300字左右的养生视频文案。
        要求：
        1. 内容要科学准确，符合中医养生理论
        2. 语言通俗易懂，适合短视频传播
        3. 结构清晰，有开头、主体和结尾
        4. 语调亲切自然，有感染力
        5. 字数控制在280-320字之间
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            script = response.choices[0].message.content.strip()
            return script
            
        except Exception as e:
            print(f"生成文案时出错: {e}")
            return None
    
    def generate_scene_prompts(self, script):
        """
        根据文案生成10个分镜提示词
        """
        system_prompt = """
        你是一个专业的视频分镜师。请根据提供的养生视频文案，生成10个分镜提示词。
        要求：
        1. 每个提示词要描述具体的画面内容
        2. 画面要与文案内容相关，能够很好地配合文案
        3. 画面要美观、温馨，符合养生主题
        4. 提示词要适合AI图像生成，描述清晰具体
        5. 包含人物、场景、物品等元素
        6. 画面风格要统一，色调温暖
        
        请按以下格式输出：
        1. [第一个分镜提示词]
        2. [第二个分镜提示词]
        ...
        10. [第十个分镜提示词]
        """
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"文案内容：\n{script}"}
                ],
                max_tokens=800,
                temperature=0.8
            )
            
            scene_text = response.choices[0].message.content.strip()
            
            # 解析分镜提示词
            scenes = []
            lines = scene_text.split('\n')
            for line in lines:
                # 匹配数字开头的行
                match = re.match(r'^\d+\.\s*(.+)', line.strip())
                if match:
                    scenes.append(match.group(1).strip())
            
            # 确保有10个分镜
            if len(scenes) < 10:
                print(f"警告：只生成了{len(scenes)}个分镜，少于预期的10个")
            
            return scenes[:10]  # 只取前10个
            
        except Exception as e:
            print(f"生成分镜提示词时出错: {e}")
            return None
