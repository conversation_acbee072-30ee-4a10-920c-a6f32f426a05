#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安装和设置脚本
用于快速设置项目环境
"""

import os
import sys
import subprocess
import shutil

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("请使用Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True

def check_pip():
    """检查pip是否可用"""
    print("检查pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        return False

def install_dependencies():
    """安装依赖包"""
    print("安装依赖包...")
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        
        # 安装依赖
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def create_env_file():
    """创建.env文件"""
    print("创建配置文件...")
    
    if os.path.exists(".env"):
        print("✅ .env文件已存在")
        return True
    
    if os.path.exists(".env.example"):
        try:
            shutil.copy(".env.example", ".env")
            print("✅ 已从.env.example创建.env文件")
            print("请编辑.env文件，填入您的API密钥")
            return True
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    else:
        print("❌ .env.example文件不存在")
        return False

def create_directories():
    """创建必要的目录"""
    print("创建输出目录...")
    
    directories = [
        "output",
        "output/images",
        "output/audio", 
        "output/videos",
        "temp"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
            return False
    
    return True

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    modules = [
        ("openai", "OpenAI API"),
        ("requests", "HTTP请求"),
        ("PIL", "图像处理"),
        ("gtts", "语音合成"),
        ("pydub", "音频处理"),
        ("moviepy", "视频处理"),
        ("numpy", "数值计算"),
        ("dotenv", "环境变量")
    ]
    
    failed_modules = []
    
    for module, description in modules:
        try:
            __import__(module)
            print(f"✅ {description} ({module})")
        except ImportError:
            print(f"❌ {description} ({module})")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_modules)}")
        print("请检查依赖包安装是否成功")
        return False
    else:
        print("✅ 所有模块导入成功")
        return True

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*50)
    print("设置完成！后续步骤：")
    print("="*50)
    print("1. 编辑 .env 文件，填入您的API密钥：")
    print("   - OPENAI_API_KEY (必需)")
    print("   - STABILITY_API_KEY (可选)")
    print("   - ELEVENLABS_API_KEY (可选)")
    print()
    print("2. 运行测试脚本验证功能：")
    print("   python test_basic.py")
    print()
    print("3. 运行主程序开始生成视频：")
    print("   python main.py")
    print()
    print("API密钥获取地址：")
    print("- OpenAI: https://platform.openai.com/")
    print("- Stability AI: https://platform.stability.ai/")
    print("- ElevenLabs: https://elevenlabs.io/")
    print("="*50)

def main():
    """主函数"""
    print("="*50)
    print("养生视频生成器 - 环境设置")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查pip
    if not check_pip():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建配置文件
    if not create_env_file():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 测试导入
    if not test_imports():
        return False
    
    # 显示后续步骤
    show_next_steps()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 设置过程中出现错误，请检查上述信息")
        sys.exit(1)
    else:
        print("\n✅ 环境设置完成！")
