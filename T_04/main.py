#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from datetime import datetime
from config import Config
from text_generator import TextGenerator
from image_generator import ImageGenerator
from audio_generator import AudioGenerator
from video_composer import VideoComposer

class HealthVideoGenerator:
    def __init__(self):
        # 创建必要的目录
        Config.create_directories()
        
        # 初始化各个模块
        self.text_gen = TextGenerator()
        self.image_gen = ImageGenerator()
        self.audio_gen = AudioGenerator()
        self.video_composer = VideoComposer()
    
    def generate_video(self, user_prompt):
        """
        主要的视频生成流程
        """
        print("=" * 50)
        print("开始生成养生视频")
        print("=" * 50)
        
        # 生成时间戳用于文件命名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 步骤1: 生成文案
            print("\n步骤1: 生成文案...")
            script = self.text_gen.generate_script(user_prompt)
            if not script:
                print("文案生成失败")
                return None
            
            print(f"生成的文案:\n{script}")
            
            # 保存文案
            script_path = os.path.join(Config.OUTPUT_DIR, f"script_{timestamp}.txt")
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script)
            
            # 步骤2: 生成分镜提示词
            print("\n步骤2: 生成分镜提示词...")
            scene_prompts = self.text_gen.generate_scene_prompts(script)
            if not scene_prompts:
                print("分镜提示词生成失败")
                return None
            
            print(f"生成了{len(scene_prompts)}个分镜提示词:")
            for i, prompt in enumerate(scene_prompts, 1):
                print(f"{i}. {prompt}")
            
            # 保存分镜提示词
            prompts_path = os.path.join(Config.OUTPUT_DIR, f"scene_prompts_{timestamp}.txt")
            with open(prompts_path, 'w', encoding='utf-8') as f:
                for i, prompt in enumerate(scene_prompts, 1):
                    f.write(f"{i}. {prompt}\n")
            
            # 步骤3: 生成图像
            print("\n步骤3: 生成图像...")
            image_paths = self.image_gen.generate_images_from_prompts(scene_prompts)
            if not image_paths:
                print("图像生成失败")
                return None
            
            print(f"成功生成{len(image_paths)}张图像")
            
            # 步骤4: 生成配音
            print("\n步骤4: 生成配音...")
            voiceover_path = self.audio_gen.generate_voiceover(script)
            if not voiceover_path:
                print("配音生成失败")
                return None
            
            # 步骤5: 创建背景音乐（可选）
            print("\n步骤5: 准备背景音乐...")
            # 获取配音时长来创建背景音乐
            from pydub import AudioSegment
            voiceover_audio = AudioSegment.from_mp3(voiceover_path)
            duration_seconds = len(voiceover_audio) / 1000.0
            
            bg_music_path = self.audio_gen.create_background_music(duration_seconds)
            
            # 步骤6: 混合音频
            print("\n步骤6: 混合音频...")
            final_audio_path = os.path.join(Config.AUDIO_DIR, f"final_audio_{timestamp}.mp3")
            mixed_audio_path = self.audio_gen.mix_audio(voiceover_path, bg_music_path, final_audio_path)
            
            if not mixed_audio_path:
                # 如果混合失败，使用原始配音
                mixed_audio_path = voiceover_path
            
            # 步骤7: 合成视频
            print("\n步骤7: 合成最终视频...")
            output_video_path = os.path.join(Config.VIDEOS_DIR, f"health_video_{timestamp}.mp4")
            
            final_video_path = self.video_composer.create_video_from_images(
                image_paths, mixed_audio_path, output_video_path
            )
            
            if final_video_path:
                print("\n" + "=" * 50)
                print("视频生成完成!")
                print(f"输出文件: {final_video_path}")
                print("=" * 50)
                return final_video_path
            else:
                print("视频合成失败")
                return None
                
        except Exception as e:
            print(f"生成视频时出现错误: {e}")
            return None
    
    def check_api_keys(self):
        """
        检查API密钥配置
        """
        missing_keys = []
        
        if not Config.OPENAI_API_KEY:
            missing_keys.append("OPENAI_API_KEY")
        
        if missing_keys:
            print("警告: 以下API密钥未配置:")
            for key in missing_keys:
                print(f"  - {key}")
            print("\n请在.env文件中配置这些密钥，或设置环境变量")
            print("OpenAI API密钥是必需的，其他密钥为可选")
            return False
        
        return True

def main():
    """
    主函数
    """
    print("养生视频自动生成器")
    print("=" * 30)
    
    # 创建生成器实例
    generator = HealthVideoGenerator()
    
    # 检查API密钥
    if not generator.check_api_keys():
        print("请配置必要的API密钥后重试")
        return
    
    # 获取用户输入
    print("\n请输入您的提示词（例如：枸杞有什么养生功效，帮我以此为选题，生成一篇300字左右的短视频文案）:")
    user_prompt = input("> ")
    
    if not user_prompt.strip():
        print("提示词不能为空")
        return
    
    # 生成视频
    result = generator.generate_video(user_prompt)
    
    if result:
        print(f"\n视频已成功生成: {result}")
        
        # 询问是否打开输出目录
        try:
            open_folder = input("\n是否打开输出目录查看结果? (y/n): ").lower().strip()
            if open_folder == 'y':
                import subprocess
                import platform
                
                if platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", Config.VIDEOS_DIR])
                elif platform.system() == "Windows":
                    subprocess.run(["explorer", Config.VIDEOS_DIR])
                else:  # Linux
                    subprocess.run(["xdg-open", Config.VIDEOS_DIR])
        except:
            pass
    else:
        print("\n视频生成失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()
