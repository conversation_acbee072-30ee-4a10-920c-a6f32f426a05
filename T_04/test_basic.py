#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基本功能测试脚本
用于测试各个模块的基本功能
"""

import os
from config import Config
from text_generator import TextGenerator
from image_generator import ImageGenerator
from audio_generator import AudioGenerator

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    Config.create_directories()
    
    # 检查目录是否创建成功
    directories = [
        Config.OUTPUT_DIR,
        Config.IMAGES_DIR,
        Config.AUDIO_DIR,
        Config.VIDEOS_DIR,
        Config.TEMP_DIR
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✓ 目录创建成功: {directory}")
        else:
            print(f"✗ 目录创建失败: {directory}")

def test_text_generator():
    """测试文案生成模块"""
    print("\n测试文案生成模块...")
    
    if not Config.OPENAI_API_KEY:
        print("✗ 未配置OpenAI API密钥，跳过文案生成测试")
        return
    
    try:
        text_gen = TextGenerator()
        
        # 测试文案生成
        test_prompt = "枸杞的养生功效"
        print(f"测试提示词: {test_prompt}")
        
        script = text_gen.generate_script(test_prompt)
        if script:
            print(f"✓ 文案生成成功，长度: {len(script)}字")
            print(f"文案预览: {script[:100]}...")
            
            # 测试分镜生成
            scenes = text_gen.generate_scene_prompts(script)
            if scenes:
                print(f"✓ 分镜生成成功，数量: {len(scenes)}个")
                for i, scene in enumerate(scenes[:3], 1):
                    print(f"  {i}. {scene}")
                if len(scenes) > 3:
                    print(f"  ... 还有{len(scenes)-3}个分镜")
            else:
                print("✗ 分镜生成失败")
        else:
            print("✗ 文案生成失败")
            
    except Exception as e:
        print(f"✗ 文案生成模块测试失败: {e}")

def test_audio_generator():
    """测试音频生成模块"""
    print("\n测试音频生成模块...")
    
    try:
        audio_gen = AudioGenerator()
        
        # 测试TTS
        test_text = "这是一个测试文本，用于验证语音合成功能。"
        print(f"测试文本: {test_text}")
        
        audio_path = audio_gen.generate_tts_with_gtts(test_text, "test_tts.mp3")
        if audio_path and os.path.exists(audio_path):
            print(f"✓ 语音合成成功: {audio_path}")
        else:
            print("✗ 语音合成失败")
            
    except Exception as e:
        print(f"✗ 音频生成模块测试失败: {e}")

def test_image_generator():
    """测试图像生成模块"""
    print("\n测试图像生成模块...")
    
    try:
        image_gen = ImageGenerator()
        
        # 创建测试占位图像
        test_filename = "test_placeholder.png"
        placeholder_path = image_gen.create_placeholder_image(test_filename, "测试图像")
        
        if placeholder_path and os.path.exists(placeholder_path):
            print(f"✓ 占位图像创建成功: {placeholder_path}")
        else:
            print("✗ 占位图像创建失败")
            
        # 如果有API密钥，测试真实图像生成
        if Config.OPENAI_API_KEY:
            print("检测到OpenAI API密钥，可以测试DALL-E图像生成")
        if Config.STABILITY_API_KEY:
            print("检测到Stability AI API密钥，可以测试Stable Diffusion图像生成")
            
    except Exception as e:
        print(f"✗ 图像生成模块测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("养生视频生成器 - 基本功能测试")
    print("=" * 50)
    
    # 测试各个模块
    test_config()
    test_text_generator()
    test_audio_generator()
    test_image_generator()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    # 显示API密钥配置状态
    print("\nAPI密钥配置状态:")
    print(f"OpenAI API: {'✓ 已配置' if Config.OPENAI_API_KEY else '✗ 未配置'}")
    print(f"Stability AI API: {'✓ 已配置' if Config.STABILITY_API_KEY else '✗ 未配置'}")
    print(f"ElevenLabs API: {'✓ 已配置' if Config.ELEVENLABS_API_KEY else '✗ 未配置'}")
    
    print("\n提示:")
    print("- OpenAI API密钥是必需的")
    print("- 其他API密钥为可选，用于提升生成质量")
    print("- 请在.env文件中配置API密钥")

if __name__ == "__main__":
    main()
