@echo off
REM 养生视频生成器 - 演示脚本
REM 适用于 Windows

echo ========================================
echo 养生视频生成器 - 演示模式
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python
    goto :python_found
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    set PYTHON_CMD=python3
    goto :python_found
)

echo ❌ 错误: 未找到Python
echo 请先安装Python 3.8或更高版本
pause
exit /b 1

:python_found
echo ✅ 使用Python命令: %PYTHON_CMD%

REM 检查demo.py是否存在
if not exist "demo.py" (
    echo ❌ 错误: 未找到demo.py文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 找到演示文件
echo.

echo 正在启动演示模式...
echo 提示: 直接按回车使用默认示例
echo.

%PYTHON_CMD% demo.py

echo.
echo ========================================
echo 演示完成！
echo ========================================
echo.
echo 下一步：
echo 1. 运行 %PYTHON_CMD% setup.py 安装完整环境
echo 2. 配置 .env 文件中的API密钥
echo 3. 运行 %PYTHON_CMD% main.py 开始真实生成
echo.

pause
