import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    # API配置
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    STABILITY_API_KEY = os.getenv('STABILITY_API_KEY', '')  # Stable Diffusion API
    ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY', '')  # 可选，高质量语音
    
    # 视频配置
    VIDEO_WIDTH = 1080
    VIDEO_HEIGHT = 1920  # 竖屏格式，适合短视频
    VIDEO_FPS = 30
    IMAGE_DURATION = 3  # 每张图片显示时长（秒）
    
    # 音频配置
    TTS_LANGUAGE = 'zh'  # 中文
    BACKGROUND_MUSIC_VOLUME = 0.3  # 背景音乐音量
    
    # 文件路径
    OUTPUT_DIR = 'output'
    IMAGES_DIR = 'output/images'
    AUDIO_DIR = 'output/audio'
    VIDEOS_DIR = 'output/videos'
    TEMP_DIR = 'temp'
    
    # 创建必要的目录
    @staticmethod
    def create_directories():
        directories = [
            Config.OUTPUT_DIR,
            Config.IMAGES_DIR,
            Config.AUDIO_DIR,
            Config.VIDEOS_DIR,
            Config.TEMP_DIR
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
