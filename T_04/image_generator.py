import requests
import base64
import os
from PIL import Image
from io import BytesIO
from config import Config
import time

class ImageGenerator:
    def __init__(self):
        self.stability_api_key = Config.STABILITY_API_KEY
        self.base_url = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"
    
    def generate_image_with_stability(self, prompt, filename):
        """
        使用Stability AI生成图像
        """
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.stability_api_key}",
        }
        
        # 优化提示词，添加养生视频风格
        enhanced_prompt = f"{prompt}, warm lighting, soft colors, peaceful atmosphere, high quality, cinematic, 4k"
        
        body = {
            "text_prompts": [
                {
                    "text": enhanced_prompt,
                    "weight": 1
                }
            ],
            "cfg_scale": 7,
            "height": Config.VIDEO_HEIGHT,
            "width": Config.VIDEO_WIDTH,
            "samples": 1,
            "steps": 30,
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=body)
            
            if response.status_code != 200:
                print(f"生成图像失败: {response.status_code}, {response.text}")
                return None
            
            data = response.json()
            
            # 保存图像
            for i, image in enumerate(data["artifacts"]):
                image_data = base64.b64decode(image["base64"])
                image_path = os.path.join(Config.IMAGES_DIR, filename)
                
                with open(image_path, "wb") as f:
                    f.write(image_data)
                
                return image_path
                
        except Exception as e:
            print(f"生成图像时出错: {e}")
            return None
    
    def generate_image_with_dalle(self, prompt, filename):
        """
        使用DALL-E生成图像（备用方案）
        """
        try:
            from openai import OpenAI
            client = OpenAI(api_key=Config.OPENAI_API_KEY)
            
            enhanced_prompt = f"{prompt}, warm and peaceful style, suitable for wellness video"
            
            response = client.images.generate(
                model="dall-e-3",
                prompt=enhanced_prompt,
                size="1024x1792",  # 接近竖屏比例
                quality="standard",
                n=1,
            )
            
            image_url = response.data[0].url
            
            # 下载图像
            img_response = requests.get(image_url)
            if img_response.status_code == 200:
                image_path = os.path.join(Config.IMAGES_DIR, filename)
                
                # 调整图像尺寸
                img = Image.open(BytesIO(img_response.content))
                img = img.resize((Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT), Image.Resampling.LANCZOS)
                img.save(image_path)
                
                return image_path
            
        except Exception as e:
            print(f"使用DALL-E生成图像时出错: {e}")
            return None
    
    def generate_images_from_prompts(self, scene_prompts):
        """
        根据分镜提示词批量生成图像
        """
        image_paths = []
        
        for i, prompt in enumerate(scene_prompts):
            print(f"正在生成第{i+1}张图像...")
            filename = f"scene_{i+1:02d}.png"
            
            # 首先尝试使用Stability AI
            if self.stability_api_key:
                image_path = self.generate_image_with_stability(prompt, filename)
            else:
                # 备用方案：使用DALL-E
                image_path = self.generate_image_with_dalle(prompt, filename)
            
            if image_path:
                image_paths.append(image_path)
                print(f"图像生成成功: {image_path}")
            else:
                print(f"第{i+1}张图像生成失败")
                # 创建占位图像
                placeholder_path = self.create_placeholder_image(filename, f"Scene {i+1}")
                image_paths.append(placeholder_path)
            
            # 添加延迟避免API限制
            time.sleep(1)
        
        return image_paths
    
    def create_placeholder_image(self, filename, text):
        """
        创建占位图像
        """
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建纯色背景
        img = Image.new('RGB', (Config.VIDEO_WIDTH, Config.VIDEO_HEIGHT), color=(200, 220, 200))
        draw = ImageDraw.Draw(img)
        
        # 添加文字
        try:
            font = ImageFont.truetype("arial.ttf", 60)
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (Config.VIDEO_WIDTH - text_width) // 2
        y = (Config.VIDEO_HEIGHT - text_height) // 2
        
        draw.text((x, y), text, fill=(100, 100, 100), font=font)
        
        # 保存图像
        image_path = os.path.join(Config.IMAGES_DIR, filename)
        img.save(image_path)
        
        return image_path
